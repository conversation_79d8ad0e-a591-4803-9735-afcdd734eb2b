import cv from './cv';
import ws_protocol from '../proto/ws_protocol';
import world_pb = ws_protocol.pb;
import gs_protocol from '../proto/gs_protocol';
import game_pb = gs_protocol.protocol;
import { ActionType, CardNum, CardSuit, CreateGameMode } from './tools/Enum';
import {
    CardItem,
    NoticeCommunityCards,
    NoticeGameBlind,
    NoticeGameElectDealer,
    NoticeGameHolecard,
    NoticeResetGame,
} from './data/RoomData';
import { GameState, transformCardItemsToSymbol } from './state';
import pkwRoom from './pkwRoom';
import {
    DelayService,
    SHARED_ACTION,
    logging,
    fetchStrategy,
    GameMode,
    GameType,
    JobDataHandler,
    UserStatus,
    PlayerStats,
} from 'shared';

function sleep(duration: number) {
    return new Promise((resolve) => setTimeout(resolve, duration));
}

function actionTypeToSharedAction(actionType: ActionType): SHARED_ACTION {
    if (actionType === ActionType.Enum_Action_Raise) return SHARED_ACTION.RAISE;
    else if (actionType === ActionType.Enum_Action_Allin) return SHARED_ACTION.ALL_IN;
    else if (actionType === ActionType.Enum_Action_Bet) return SHARED_ACTION.BET;
    else if (actionType === ActionType.Enum_Action_Check) return SHARED_ACTION.CHECK;
    else if (actionType === ActionType.Enum_Action_Call) return SHARED_ACTION.CALL;
    else return SHARED_ACTION.FOLD;
}

export enum RoundAction {
    check,
    raise,
    allIn,
    call,
    // addAllIn,
    fold,
}


type ActionTimeType = {
    stopwatch?: number;
};

const BOMP_MODE_CARDS_MAX_WAIT_MS = 2000;

export class pkwGame {
    private _selfUid: number = null;
    private _roomSt: world_pb.IClubGameSnapshotV3 = null;
    private _gameSt: game_pb.NoticeGameSnapshot = null;
    private _pActionTurn: game_pb.NoticePlayerActionTurn = null;
    private _selfInfo: game_pb.IPlayerInfo = null;

    private _greatestBet: number = 0; // the greatest bet amount in this round

    private _updateProgressCb: JobDataHandler = () => {};
    public _state: GameState = new GameState('', '', 0, 0, GameMode.NORMAL);
    private _stats: PlayerStats = null;
    public _currentStack: number = 0;
    private _previousGameStake: number = 0;
    private _lastWin: number = 0;
    private _lastKnownRake: number = 0;

    private delayService: DelayService;
    private _gameModeCode: GameMode = GameMode.NORMAL;
    private _gameTypeCode: GameType = GameType.NLHE;
    private _profileName?: string;

    public updateInfo(roomSt?: world_pb.IClubGameSnapshotV3, gameSt?: game_pb.NoticeGameSnapshot) {
        this._selfUid = cv.dataHandler.getUserData().u32Uid;
        this._roomSt = roomSt;
        this._gameSt = gameSt;
        this._gameTypeCode =
            this._roomSt.game_mode === CreateGameMode.CreateGame_Mode_Short
                ? GameType.SHORTDECK
                : GameType.NLHE;
        logging.info(`[state] updateInfo:`, {
            ante: this._roomSt.ante,
            big_blind: this._roomSt.big_blind,
            gameModeCode: this._gameModeCode,
            gameTypeCode: this._gameTypeCode,
        });
        const gameuuid = this._roomSt.room_id + ':' + this._selfUid + ':' + this._roomSt.start_time;
        this._state = new GameState(
            gameuuid,
            this._gameSt.roomid.toString(),
            this._gameTypeCode === GameType.SHORTDECK ? this._roomSt.ante : this._roomSt.big_blind,
            this._roomSt.ante,
            this._gameModeCode,
            this._gameTypeCode,
        );
        logging.setUserId(this._selfUid);
        logging.setRoomId(this._gameSt.roomid);
    }

    public init() {
        this._regMsg();
    }

    public getSelfUid() {
        return this._selfUid;
    }

    public updateStatus(status: UserStatus) {
        this._updateProgressCb({ stats: this._stats, status: status });
    }

    public setUpdateProgressCb(cb: JobDataHandler) {
        this._updateProgressCb = cb;
    }

    public setGameModeCode(gameModeCode: GameMode) {
        this._gameModeCode = gameModeCode;
    }

    public setDelayService(delayService: DelayService) {
        this.delayService = delayService;
    }

    public setProfileName(profileName?: string) {
        this._profileName = profileName;
    }

    private _regMsg() {
        cv.MessageCenter.register('on_game_holecard_noti', this.OnGameHoleCardNoti.bind(this), this);

        cv.MessageCenter.register('on_game_action_turn_noti', this.OnActionTurn.bind(this), this);
        cv.MessageCenter.register('on_game_action_noti', this.OnPlayerAction.bind(this), this);
        cv.MessageCenter.register('on_game_elect_dealer_noti', this.OnGameElectDealer.bind(this), this);
        cv.MessageCenter.register('on_game_communitycard_noti', this.OnCommunityCard.bind(this), this);
        cv.MessageCenter.register('on_game_blind_noti', this.OnGameBlindNoti.bind(this), this);
        cv.MessageCenter.register('on_resetgame_noti', this.OnResetGameNoti.bind(this), this);
        cv.MessageCenter.register('on_game_anti_noti', this.OnAnteNoti.bind(this), this);
        cv.MessageCenter.register('on_game_settlement_noti', this.onGameSettlementNotice.bind(this), this);
        cv.MessageCenter.register('on_room_situation', this.onRoomSituation.bind(this), this);
    }

    private OnGameHoleCardNoti(msg: NoticeGameHolecard) {
        logging.withTag('PKW_GAME').info('[InGame] OnGameHoleCardNoti', { payload: msg });
        if (this._selfUid == null) {
            logging.warn('[InGame] OnGameHoleCardNoti - selfUid is null');
            return;
        }

        // There could be a case where the card in OnGameHoleCardNoti are hidden (number: 256, suit: 256)
        const holeCards = transformCardItemsToSymbol(msg.holdcards);
        if (!holeCards) {
            logging.warn('[InGame] OnGameHoleCardNoti  - holecards are hidden... skipping', {});
            return;
        }

        let currentPlayer = this._state.getCurrentPlayer(this._selfUid.toString());
        if (currentPlayer) {
            currentPlayer.setHoleCards(holeCards);
        } else {
            logging.warn('[InGame] OnGameHoleCardNoti - currentPlayer not found', {
                selfUid: this._selfUid,
                players: this._state?.players,
            });
        }
    }

    public updateCurrentStack(_newStack: number, source?: string) {
        // Additional check to ensure the new stack is a number and not a protobuf long integer
        const newStack = Number(_newStack);
        logging.info(
            `Current stack updated: ${this._currentStack} -> ${newStack}, ${source ? `source: ${source}` : ''}`,
        );
        this._currentStack = newStack;
        logging.setStack(newStack);
    }

    private zoomOnRoomSituation(msg: game_pb.NoticeRoomSituation) {
        const player = msg.buyin_player_list.find((p) => p.playerid == this._selfUid);
        const newStack = Number(player?.total_buyin || 0) + Number(player?.curr_record || 0);
        if (newStack <= 0) {
            return;
        }

        this.updateCurrentStack(newStack);

        this.checkIfActualBuyInIsLowerThanRequestedBuyIn();

        this.updatePlayerStats();

        this.checkIfRebuyNeeded();
        // Withdraw is feature that allows user to withdraw chips during the game if the stake is too high
        this.checkIfWithdrawNeeded();
    }

    private onRoomSituation(situation: game_pb.NoticeRoomSituation) {
        logging.withTag('SDK').info('[Game] onRoomSituation');

        for (const player of situation.buyin_player_list) {
            if (Number(player.playerid) === this._selfUid) {
                pkwRoom.setTotalBuyIn(Number(player.total_buyin));
                pkwRoom.setHandsPlayed(Number(player.HandCount));
                break;
            }
        }

        // This is a workaround for R7 (Zoom)
        // We need to request buyin not in OnResetGameNoti as usual,
        // because OnResetGameNoti is not called in Zoom
        if (cv.roomManager.currentGameIsZoom()) {
            this.zoomOnRoomSituation(situation);
        }
    }

    private onGameSettlementNotice(msg: game_pb.NoticeGameSettlement) {
        // write down current stake on the end of the game
        this._lastWin = msg.winners.find((w) => w.playerid == this._selfUid)?.amount || 0;
        this._previousGameStake = (this._selfInfo?.stake ?? 0) + this._lastWin;
        logging.info(
            `[InGame] onGameSettlementNotice: Stake - ${this._previousGameStake}, Win - ${this._lastWin}`,
        );
    }

    private OnAnteNoti(msg: any) {
        logging.info('[InGame] OnAnteNoti:', JSON.stringify(msg));
        if (msg && msg.amount_list) {
            let ante = Math.max(...msg.amount_list);
            if (ante > this._roomSt?.ante) {
                logging
                    .withTag('PKW')
                    .info(
                        `[state] Changed game mode: ${this._state.game_mode_code} --> ${GameMode.BOMB}; ante: ${ante}; current ante: ${this._roomSt?.ante}; current big_blind: ${this._roomSt.big_blind}`,
                    );
                this._state.updateAnteGameMode(GameMode.BOMB, ante);
            }
        }
    }

    //dealer, BB, Sb pos
    //msg from : game_pb.MSGID.MsgID_Game_ElectDealer_Notice
    private OnGameElectDealer(msg: NoticeGameElectDealer) {
        logging.info('[InGame] OnGameElectDealer', msg);
        if (this._gameTypeCode !== GameType.SHORTDECK) {
            this._state.sb_seat = msg.sb_seateid;
            this._state.bb_seat = msg.bb_seatid;
        }
        this._state.dealer_seat = msg.dealer_seatid;
    }

    // get each player stake
    public OnResetGameNoti(msg: NoticeResetGame) {
        // Zoom: the room can be changed with each new hand, need to update the room id
        if (this._gameSt && msg.roomid && this._gameSt.roomid != msg.roomid) {
            logging.info('[InGame] OnResetGameNoti - roomId changed', {
                prevRoomId: this._gameSt.roomid,
                newRoomId: msg.roomid,
                payload: msg,
            });
            this._gameSt.roomid = msg.roomid;
            logging.setRoomId(this._gameSt.roomid);
        }

        logging.resetRoundValues();
        if (this._roomSt) {
            logging.info('Resetting game state', {
                ante: this._roomSt.ante,
                bigBlind: this._roomSt.big_blind,
            });
            this._state.resetState({
                gameModeCode: this._gameModeCode,
                ante: this._roomSt.ante,
            });
        } else {
            logging.warn('Room state is null, cannot reset game state');
        }
        msg.players
            .filter((player) => player.in_game)
            .filter((player) => player.stake > 0)
            .forEach((player) => {
                this._state.addPlayer(player.playerid.toString(), player.seatid, player.stake);
                if (!this._selfUid) {
                    this._selfUid = cv.dataHandler.getUserData().u32Uid;
                    // if selfUid is not set yet, we can use request buy in amount
                    this.updateCurrentStack(pkwRoom.getRequestBuyInAmount(), 'pkwRoom');
                }
                if (player.playerid == this._selfUid) {
                    this.updateCurrentStack(player.stake, 'OnResetGameNoti');
                }
            });

        this.checkIfActualBuyInIsLowerThanRequestedBuyIn();
        this.checkAutoBuyIn();

        this.updatePlayerStats();
        this.checkIfRebuyNeeded();
        // Withdraw is feature that allows user to withdraw chips during the game if the stake is too high
        this.checkIfWithdrawNeeded();
    }

    private checkIfActualBuyInIsLowerThanRequestedBuyIn() {
        // in some case the real buyIn can be lower than we requested
        logging
            .withTag('BUYIN')
            .info(
                `[InGame] checkIfActualBuyInIsLowerThanRequestedBuyIn - beginGameStake: ${this._currentStack}, previousGameStake: ${this._previousGameStake}`,
            );
        const actualBuyIn = this._currentStack - this._previousGameStake;
        if (this._lastWin > 0 && !pkwRoom.getRebuyWasMade()) {
            // If players wins, the rake will be taken from him
            // We can understand it by compairing the previous game final stake and the stake at the beginning of the game
            // If player won, and requested rebuy in the last game, he will het + rebuy - rake, that's why we won't show rake here
            // The formula for rake calculation is currently unknown
            this._lastKnownRake = this._previousGameStake - this._currentStack;
            logging
                .withTag('BUYIN')
                .info(
                    `[InGame] checkIfActualBuyInIsLowerThanRequestedBuyIn - RAKE: ${this._lastKnownRake}, Last Win: ${this._lastWin}`,
                );
        }

        if (pkwRoom.getRebuyWasMade()) {
            if (actualBuyIn + this._lastKnownRake < pkwRoom.getBuyInAmount()) {
                logging
                    .withTag('BUYIN')
                    .info(
                        `[InGame] checkIfActualBuyInIsLowerThanRequestedBuyIn - rebuy (${actualBuyIn}) was made ` +
                            `and it is lower than requested buyIn (${pkwRoom.getBuyInAmount()})`,
                    );
            }

            pkwRoom.setRebuyWasMade(false);
        }
    }

    private checkAutoBuyIn() {
        // We need this if user returns to the room and does not need to buy in or the buy was made automatically
        if (pkwRoom.getBuyInAmount() == 0) {
            pkwRoom.setBuyInAmount(this._currentStack);
        }
    }

    private updatePlayerStats() {
        this._stats = {
            handsPlayed: pkwRoom.getHandsPlayed(),
            totalBuyIn: pkwRoom.getTotalBuyIn(),
            lastBuyIn: pkwRoom.getBuyInAmount(),
            rebuyCount: pkwRoom.getRebuyCount(),
            stack: this._currentStack,
        };
    }

    /**
     roomid: number = 0;
     sb_amount: number = 0;
     bb_amount: number = 0;
     straddle_seat_list: number[] = [];  // straddle seat id list
     straddle_amount_list: number[] = []; //straddle amount list
     post_seat_list: number[] = []; // Post seat id list
     sb_seatid: number = 0;
     bb_seatid: number = 0;
     dealer_seatid: number = 0;
     * */
    private OnGameBlindNoti(msg: NoticeGameBlind) {
        logging.info('[InGame] OnGameBlindNoti', { payload: msg });
        if (msg.straddle_seat_list.length > 0) {
            this._state.straddle_seat = msg.straddle_seat_list[0];
        }
        if (msg.post_seat_list.length > 0) {
            this._state.post_seats = msg.post_seat_list;
        }
    }

    private updateGreatestBet() {
        this._greatestBet = 0;
        for (const player of this._pActionTurn.players) {
            if (player.playerid == this._selfUid) {
                this._selfInfo = player;
                logging.setPlayerName(player.name);
            }
            this._greatestBet = Math.max(this._greatestBet, player.round_bet);
        }
    }

    // private printPot() {
    //     let pot: number = 0;
    //     this._pActionTurn.pots.forEach((p) => {
    //         pot += p.amount;
    //     });
    //     console.log('[InGame] Current Pot: ' + (pot / 100).toFixed(2));
    // }

    //your move now, send your action
    private async OnActionTurn(pkActionTurn: game_pb.NoticePlayerActionTurn) {
        // if it's not our turn, we don't need to act
        this._pActionTurn = pkActionTurn;
        this.updateGreatestBet();
        if (pkActionTurn.curr_action_uid !== this._selfUid) {
            return;
        }
        logging.info('[InGame] OnActionTurn');
        const stopwatch = Date.now();

        let currentPlayer = this._state.getCurrentPlayer(this._selfUid.toString());
        if (!currentPlayer) {
            const actionTurnPlayer = pkActionTurn.players.find((p) => p.playerid === this._selfUid);
            if (actionTurnPlayer) {
                logging.warn(
                    '[InGame] OnActionTurn - self player not found, adding player from pkActionTurn',
                    { actionTurnPlayer },
                );
                this._state.addPlayer(
                    actionTurnPlayer.playerid.toString(),
                    actionTurnPlayer.seatid,
                    actionTurnPlayer.stake,
                );
                currentPlayer = this._state.getCurrentPlayer(this._selfUid.toString());
            } else {
                logging.error(
                    '[InGame] OnActionTurn - self player not found, calling fallback action...',
                    'Self player not found',
                    { selfId: this._selfUid, state: this._state, pkActionTurn },
                );
                this.callFallbackAction(stopwatch);
                return;
            }
        }

        if (pkActionTurn.holdcards) {
            const holeCards = transformCardItemsToSymbol(pkActionTurn.holdcards);
            currentPlayer.setHoleCards(holeCards);
        }
        if (!currentPlayer.getCardsArray().length) {
            logging.warn('[InGame] OnActionTurn - hole cards could not be setup, calling fallback action...');
            this.callFallbackAction(stopwatch);
            return;
        }

        pkwRoom.setUserStatus(UserStatus.inGamePlay);

        // in bomb pot mode, wait for community cards event if it comes after the action turn event
        while (
            this._state.game_mode_code === GameMode.BOMB &&
            this._state.actions.entries.length === 0 &&
            Date.now() - stopwatch < BOMP_MODE_CARDS_MAX_WAIT_MS
        ) {
            await sleep(200);
        }

        // We do not send userId here because we don't want to set betProfile
        // We send profileName to set strategyProfile
        try {
            const action = await fetchStrategy({
                state: this._state,
                profileName: this._profileName,
                betProfileUserId: undefined,
            });

            // export interface GameAction {
            //     action: StrategyResponseAction | string;
            //     amount?: number;
            //     probability?: number;
            //     seat_no?: number;
            // }

            // const action = {
            //     action: 'allin',
            //     amount: 100000000000000,
            //     probability: 1,
            //     seat_no: 1,
            // };

            // console.log('IM HERE');
            // console.log('action', JSON.stringify(action, null, 2));

            // action.action = 'allin';
            // action.amount = 100000000000000;

            const ACTION_MAP = {
                fold: RoundAction.fold,
                call: RoundAction.call,
                check: RoundAction.check,
                raise: RoundAction.raise,
                allin: RoundAction.allIn,
                bet: RoundAction.raise,
            };
            const roundAction: RoundAction = ACTION_MAP[action.action];
            logging.withTag('ACTION_MAKING').info(`PkwGame - request action: ${RoundAction[roundAction]}`);

            this._reqAction(
                roundAction,
                roundAction === RoundAction.raise ? action.amount : null,
                action.probability ?? 0,
                { stopwatch },
            );
        } catch (err) {
            logging.withTag('ACTION_MAKING').error(`PkwGame - fetchStrategy error`, err?.message || err, {
                state: this._state,
            });

            this.callFallbackAction(stopwatch);
        }
    }
    private callFallbackAction(stopwatch: number) {
        const fallbackAction =
            this._greatestBet === this._selfInfo.round_bet ? RoundAction.check : RoundAction.fold;
        logging.withTag('ACTION_MAKING').info(`PkwGame - call fallback action`);
        this._reqAction(fallbackAction, null, 0, { stopwatch });
    }

    private checkIfWithdrawNeeded() {
        if (pkwRoom.getWithdrawAmount() <= 0) {
            // withdraw is disabled
            return;
        }
        // We use ante for calculating the withdraw amount if the game is shortdeck
        const blindValue =
            this._gameTypeCode === GameType.SHORTDECK ? this._roomSt.ante : this._roomSt.big_blind;
        const shouldWithdrawAt = blindValue * pkwRoom.getWithdrawThreshold();
        logging
            .withTag('SDK')
            .info(
                `[InGame] checkIfWithdrawNeeded. Current stake: ${this._currentStack}, Blind value: ${blindValue}, Should withdraw at: ${shouldWithdrawAt}`,
            );
        if (this._currentStack > shouldWithdrawAt) {
            logging.info('[InGame] Withdraw needed');
            pkwRoom.requestWithdraw();
        }
    }

    private checkIfRebuyNeeded() {
        if (!pkwRoom.getRebuyEnabled()) {
            logging.info('[InGame] Rebuy is disabled');
            return;
        }
        if (this._selfInfo == null) {
            return;
        }

        const baseValue =
            this._gameTypeCode === GameType.SHORTDECK ? this._roomSt.ante : this._roomSt.big_blind;

        const shouldRebuyAt = baseValue * pkwRoom.getRebuyThreshold();
        const rebuyNeeded = this._currentStack < shouldRebuyAt;
        logging.info('[PKW_GAME] checkIfRebuyNeeded', {
            stack: this._currentStack,
            minBuyIn: this._roomSt.buyin_min,
            baseValue,
            shouldRebuyAt,
            rebuyNeeded,
        });
        if (rebuyNeeded) {
            pkwRoom.rebuy();
        }
    }

    private printCards(cards: CardItem[], banner: string) {
        console.log(banner);
        cards.forEach((card) => {
            console.log(
                '[ ' +
                    this._getEnumName(CardNum, card.number) +
                    ' : ' +
                    this._getEnumName(CardSuit, card.suit) +
                    ' ]',
            );
        });
        console.log('-----------END Print Cards------------');
    }

    private _reqAction(
        roundAction: RoundAction,
        raiseAmount: number | null,
        actionProbability: number | null,
        actionTime: ActionTimeType,
    ) {
        switch (roundAction) {
            case RoundAction.fold:
                this.cvGameNetRequestAction(ActionType.Enum_Action_Fold, 0, actionProbability, actionTime);
                break;
            case RoundAction.check:
                if (this._greatestBet - this._selfInfo.round_bet > 0) {
                    logging.warn('[InGame] illegal move: CHECK, change to call action');
                    this._reqAction(RoundAction.call, null, actionProbability, actionTime);
                    return;
                }
                this.cvGameNetRequestAction(ActionType.Enum_Action_Check, 0, actionProbability, actionTime);
                break;
            case RoundAction.call:
                let callAmount = this._greatestBet - this._selfInfo.round_bet;
                if (this._selfInfo.stake < callAmount) {
                    logging.warn('[InGame] insufficient stake to call, change to allin action', {
                        amount: (callAmount / 100).toFixed(2),
                    });
                    this._reqAction(RoundAction.allIn, null, actionProbability, actionTime);
                    return;
                }
                this.cvGameNetRequestAction(
                    ActionType.Enum_Action_Call,
                    callAmount,
                    actionProbability,
                    actionTime,
                );
                break;
            case RoundAction.allIn:
                const allInAmount = this._selfInfo.stake + this._selfInfo.round_bet;
                this.cvGameNetRequestAction(
                    ActionType.Enum_Action_Allin,
                    allInAmount,
                    actionProbability,
                    actionTime,
                );
                break;
            case RoundAction.raise:
                let minToCall = this._greatestBet - this._selfInfo.round_bet;

                raiseAmount = raiseAmount || this._pActionTurn.minimum_bet_i64; //- (minToCall + this._selfInfo.round_bet);
                if (this._selfInfo.stake < raiseAmount || this._selfInfo.stake < minToCall) {
                    logging.warn('[InGame] insufficent stake to raise, change to allin action');
                    this._reqAction(RoundAction.allIn, null, actionProbability, actionTime);
                    return;
                }
                this.cvGameNetRequestAction(
                    ActionType.Enum_Action_Raise,
                    raiseAmount,
                    actionProbability,
                    actionTime,
                );
                break;
            default:
                this._reqAction(RoundAction.fold, null, actionProbability, actionTime);
                break;
        }
    }

    private async cvGameNetRequestAction(
        actionType: ActionType,
        amount: number,
        actionProbability: number | null,
        actionTime: ActionTimeType,
    ) {
        const gameStage = this._state.getCommunityCards().length;
        const sharedAction = actionTypeToSharedAction(actionType);
        const roomId = this._gameSt.roomid;
        const actionSeq = this._pActionTurn.ActionSeq;

        let calculatedDelayMs = this.delayService.calculateDelayMs(
            gameStage,
            sharedAction,
            actionProbability,
        );

        const isCheckBet = actionType === ActionType.Enum_Action_Fold ? true : undefined;
        const keepEnd = actionType === ActionType.Enum_Action_Fold ? 0 : undefined;

        // Time management
        const { stopwatch = Date.now() } = actionTime;
        const timePassed = new Date().getTime() - stopwatch;
        const delayLeftMs = calculatedDelayMs - timePassed;
        logging
            .withTag('DELAY')
            .info(
                `Calculated delay: ${calculatedDelayMs} (ms) for ${SHARED_ACTION[sharedAction]} on stage ${gameStage.toString()}, delay left: ${delayLeftMs} ms`,
            );

        if (delayLeftMs > 0) {
            await sleep(delayLeftMs);
        }
        logging
            .withTag('REQUEST_ACTION')
            .info(`cvGameNetRequestAction - request action ${ActionType[actionType]}, amount: ${amount}`);
        cv.gameNet.RequestAction(roomId, actionType, amount, isCheckBet, keepEnd, actionSeq);
    }

    private OnPlayerAction(pkPlayerAction: game_pb.NoticePlayerAction) {
        const actionPlayer = this._pActionTurn?.players?.find(
            (p) => p.seatid === pkPlayerAction.last_action_seat_id,
        );
        const isHero = actionPlayer?.playerid === this._selfUid;
        logging.info(
            `[OnPlayerAction] player: ${actionPlayer?.name} ${isHero ? '(hero)' : ''} request action: ${ActionType[pkPlayerAction.action_type]}, amount: ${pkPlayerAction.amount}`,
        );

        this._state.addAction(pkPlayerAction);
        this.updateStatus(pkwRoom.getUserStatus());
    }

    private OnCommunityCard(pkCommunityCards: NoticeCommunityCards) {
        const comCards = transformCardItemsToSymbol(pkCommunityCards.cards);
        logging.info(`[InGame] OnCommunityCard: ${comCards}`, {
            payload: pkCommunityCards,
        });
        this._state.addCardsAction(comCards);
    }

    private _getEnumName(enumType: any, enumValue: number): string {
        return enumType[enumValue];
    }

    private static g_instance: pkwGame;

    public static getInstance(): pkwGame {
        if (!pkwGame.g_instance) {
            pkwGame.g_instance = new pkwGame();
        }
        return pkwGame.g_instance;
    }
}

let pkwGame_instance: pkwGame;
export default pkwGame_instance = pkwGame.getInstance();
